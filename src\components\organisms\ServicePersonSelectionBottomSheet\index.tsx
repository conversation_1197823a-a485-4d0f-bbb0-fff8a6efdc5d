import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetFlatList, BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useTheme } from "@/hooks/useTheme";
import { ServicePerson } from "@/types/complaint";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { getServicePersonListAction } from "@/store/actions/complaint";
import { View, TextInput, TouchableOpacity, Text } from "react-native";
import { useDebounce } from "@/utils/useDebounce";
import {
  Container,
  Title,
  SearchRow,
  SearchInput,
  Item,
  EmptyContainer,
  Footer,
  ConfirmBar,
  ConfirmButton,
  ConfirmButtonText,
} from "./styles";

interface ServicePersonSelectionBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (servicePerson: ServicePerson) => void;
}

const ServicePersonSelectionBottomSheet: React.FC<
  ServicePersonSelectionBottomSheetProps
> = ({ isVisible, onClose, onSelect }) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selected, setSelected] = useState<ServicePerson | null>(null);

  const {
    servicePersonList = [],
    servicePersonCurrentPage = 1,
    servicePersonLastPage = 1,
    servicePersonLoadedPages = [],
    loading = false,
  } = useAppSelector((state) => state.complaints);

  const debouncedSearch = useDebounce(query, 300);
  console.log("servicePersonList", servicePersonList);
  // Fetch service persons
  const fetchServicePersons = useCallback(
    async (params: { page: number; search?: string }) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        await dispatch(getServicePersonListAction(params)).unwrap();
      } catch (error: any) {
        console.error("fetchServicePersons error:", error);
      } finally {
        setIsRefreshing(false);
      }
    },
    [dispatch]
  );

  // Load initial data when bottom sheet opens
  useEffect(() => {
    if (isVisible && servicePersonList.length === 0) {
      fetchServicePersons({ page: 1 });
    }
  }, [isVisible]);

  // Handle search
  useEffect(() => {
    if (debouncedSearch && debouncedSearch.length >= 3) {
      fetchServicePersons({ page: 1, search: debouncedSearch });
    } else if (debouncedSearch === "") {
      fetchServicePersons({ page: 1 });
    }
  }, [debouncedSearch, fetchServicePersons]);

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["70%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const handleLoadMore = useCallback(() => {
    if (servicePersonCurrentPage < servicePersonLastPage && !loading) {
      fetchServicePersons({
        page: servicePersonCurrentPage + 1,
        search: debouncedSearch,
      });
    }
  }, [
    servicePersonCurrentPage,
    servicePersonLastPage,
    loading,
    debouncedSearch,
    fetchServicePersons,
  ]);

  const handleServicePersonSelect = useCallback(
    (servicePerson: ServicePerson) => {
      setSelected(servicePerson);
    },
    []
  );

  const handleConfirm = useCallback(() => {
    if (selected) {
      onSelect(selected);
      onClose();
    }
  }, [selected, onSelect, onClose]);

  const renderServicePersonItem = useCallback(
    ({ item }: { item: ServicePerson }) => (
      <Item
        onPress={() => handleServicePersonSelect(item)}
        borderColor={theme.colors.border}
        selected={selected?.service_person_id === item.service_person_id}
        bgColor={theme.colors.primary + "22"}
      >
        <Text style={{ fontWeight: "bold" }}>{item.name}</Text>
        <Text>{item.email}</Text>
      </Item>
    ),
    [
      handleServicePersonSelect,
      selected,
      theme.colors.border,
      theme.colors.primary,
    ]
  );

  const renderEmptyComponent = useCallback(() => {
    if (loading && !isRefreshing) {
      return <LoadingOverlay isLoading={true} size="large" />;
    }
    return (
      <EmptyContainer>
        <Text style={{ color: theme.colors.text }}>
          {t("common.no_results_found")}
        </Text>
      </EmptyContainer>
    );
  }, [loading, isRefreshing, t, theme.colors.text]);

  const renderFooter = useCallback(() => {
    if (loading && servicePersonCurrentPage > 1) {
      return (
        <Footer>
          <LoadingOverlay isLoading={true} size="small" />
        </Footer>
      );
    }
    return null;
  }, [loading, servicePersonCurrentPage]);

  const renderBackdrop = useBottomSheetBackdrop();

  // Reset selection when closed
  useEffect(() => {
    if (!isVisible) setSelected(null);
  }, [isVisible]);

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      enableDynamicSizing={false}
      enablePanDownToClose
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <Container style={{ backgroundColor: theme.colors.background }}>
        <Title>{t("complaint.select_service_person")}</Title>
        <SearchRow>
          <Ionicons name="search" size={20} color={theme.colors.text} />
          <SearchInput
            placeholder={t("common.search")}
            value={query}
            onChangeText={handleSearch}
            borderColor={theme.colors.border}
            placeholderTextColor={theme.colors.gray}
          />
        </SearchRow>
      </Container>
      <BottomSheetFlatList
        data={servicePersonList}
        keyExtractor={(item) => item.service_person_id.toString()}
        renderItem={renderServicePersonItem}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={renderFooter}
        contentContainerStyle={{ paddingBottom: 80 }}
        showsVerticalScrollIndicator={false}
      />
      <ConfirmBar
        borderColor={theme.colors.border}
        bgColor={theme.colors.background}
      >
        <ConfirmButton
          onPress={handleConfirm}
          disabled={!selected}
          bgColor={selected ? theme.colors.primary : theme.colors.gray}
        >
          <ConfirmButtonText>{t("common.confirm")}</ConfirmButtonText>
        </ConfirmButton>
      </ConfirmBar>
    </BottomSheetModal>
  );
};

export default ServicePersonSelectionBottomSheet;
