import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import LoadingOverlay from "@/components/atoms/LoadingOverlay";
import { useTheme } from "@/hooks/useTheme";
import {
  Title,
  SearchContainer,
  SearchInput,
  ListContainer,
  PickerItem,
  PickerItemText,
  EmptyContainer,
  EmptyText,
  LoadingContainer,
  SectionHeaderContainer,
} from "./PickerBottomSheetStyles";
import { useBottomSheetBackdrop } from "@/utils/commonBackdrop";

export interface PickerOption {
  id: number;
  [key: string]: any;
}

interface PickerBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (item: PickerOption) => Promise<void>;
  data: PickerOption[];
  title?: string;
  searchPlaceholder?: string;
  displayKey?: string;
  isLoading?: boolean;
  searchable?: boolean;
  renderItem?: (item: PickerOption) => React.ReactNode;
  backgroundStyle?: string;
}

const   PickerBottomSheet: React.FC<PickerBottomSheetProps> = ({
  isVisible,
  onClose,
  onSelect,
  data,
  title = "Select Option",
  searchPlaceholder = "Search options",
  displayKey = "name",
  isLoading = false,
  searchable = true,
  renderItem,
  backgroundStyle,
}) => {
  const sheetRef = useRef<BottomSheetModal>(null);
  const [query, setQuery] = useState("");
  const [filtered, setFiltered] = useState<PickerOption[]>(data);
  const { theme } = useTheme();

  useEffect(() => {
    if (isVisible) {
      sheetRef.current?.present();
    } else {
      sheetRef.current?.dismiss();
    }
  }, [isVisible]);

  useEffect(() => {
    if (!query || !searchable) {
      setFiltered(data);
      return;
    }
    setFiltered(
      data.filter((item) =>
        item[displayKey]?.toString().toLowerCase().includes(query.toLowerCase())
      )
    );
  }, [query, data, displayKey, searchable]);

  const handleDismiss = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const snapPoints = useMemo(() => ["50%"], []);

  const handleSearch = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const renderPickerItem = useCallback(
    ({ item }: { item: PickerOption }) => {
      if (renderItem) {
        return (
          <PickerItem
            onPress={async () => {
              await onSelect(item);
            }}
          >
            {renderItem(item)}
          </PickerItem>
        );
      }

      return (
        <PickerItem
          onPress={async () => {
            await onSelect(item);
          }}
        >
          <PickerItemText>{item[displayKey]}</PickerItemText>
        </PickerItem>
      );
    },
    [onSelect, displayKey, renderItem]
  );

  const renderBackdrop = useBottomSheetBackdrop();

  const sections = useMemo(
    () => [{ title: "main", data: filtered }],
    [filtered]
  );

  return (
    <BottomSheetModal
      ref={sheetRef}
      snapPoints={snapPoints}
      onDismiss={handleDismiss}
      style={{ borderTopLeftRadius: 20, borderTopRightRadius: 20 }}
      backdropComponent={renderBackdrop}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: theme.colors.background }}
      handleIndicatorStyle={{ backgroundColor: theme.colors.gray }}
    >
      <ListContainer
        sections={sections}
        keyExtractor={(item: PickerOption) => item.id.toString()}
        renderSectionHeader={() => (
          <SectionHeaderContainer>
            <Title>{title}</Title>
            {searchable && (
              <SearchContainer>
                <Ionicons name="search" size={20} color={theme.colors.text} />
                <SearchInput
                  placeholder={searchPlaceholder}
                  value={query}
                  onChangeText={handleSearch}
                  placeholderTextColor={theme.colors.text}
                />
              </SearchContainer>
            )}
          </SectionHeaderContainer>
        )}
        ListEmptyComponent={
          isLoading ? (
            <LoadingContainer>
              <LoadingOverlay isLoading={isLoading} size="large" />
            </LoadingContainer>
          ) : (
            <EmptyContainer>
              <EmptyText>No options found</EmptyText>
            </EmptyContainer>
          )
        }
        renderItem={renderPickerItem}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ paddingBottom: 20 }}
        stickySectionHeadersEnabled
        showsVerticalScrollIndicator={false}
      />
    </BottomSheetModal>
  );
};

export default PickerBottomSheet;
