import React, { useState, useRef, useEffect } from "react";
import {
  ScrollView,
  Pressable,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { Header } from "@/components";
import Toast from "react-native-toast-message";
import {
  Container,
  ErrorContainer,
  ErrorText,
  ImageContainer,
  ProductImage,
  Content,
  Header as StyledHeader,
  Title,
  PriceContainer,
  PriceLabel,
  PriceValue,
  DescriptionContainer,
  SectionHeader,
  GalleryContainer,
  GalleryImage,
  CategoryContainer,
  CategoryChip,
  CategoryText,
  DocumentContainer,
  DocumentButton,
  DocumentText,
  QuantityContainer,
  QuantityButton,
  QuantityValue,
  AddToCart<PERSON>utton,
  AddToCartText,
  BottomContainer,
  StockStatus,
  StockText,
  ShortDescription,
  ShortDescriptionText,
  SectionTitle,
  LoadingContainer,
} from "@/styles/ProductDetail.styles";
import { DocumentFile, ProductType } from "@/types/product";
import { useTheme } from "@/hooks/useTheme";
import { RenderHTML } from "@/components";
import Carousel, { ICarouselInstance } from "react-native-reanimated-carousel";
import * as WebBrowser from "expo-web-browser";
import { getProductListAction } from "@/store/actions/product";
import { addToCartAction } from "@/store/actions/order";
import { UserType } from "@/types/api";

const ProductDetailScreen = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { slug } = useLocalSearchParams();
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const { theme } = useTheme();
  const carouselRef = useRef<ICarouselInstance>(null);
  const { width } = useWindowDimensions();
  const [product, setProduct] = useState<ProductType | null>(null);
  const [isAddedToCart, setIsAddedToCart] = useState(false);
  const { user } = useAppSelector((state: RootState) => state.auth);
  const isDealerPriceShown =
    user?.role_id === UserType.VENDOR &&
    user?.auth_dealer === "1" &&
    product?.display_vendor === "1";

  const fetchData = async () => {
    try {
      const res = await dispatch(
        getProductListAction({ slug: slug as string })
      ).unwrap();
      setProduct(res.data);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to fetch product details",
      });
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    if (slug) {
      fetchData();
    }
  }, [slug]);

  const thumbnailScrollRef = useRef<ScrollView>(null);
  const THUMBNAIL_WIDTH = 80;

  const goBack = () => {
    router.back();
  };

  const scrollToCenter = (index: number) => {
    const offset = index * THUMBNAIL_WIDTH - (width - THUMBNAIL_WIDTH) / 2;
    thumbnailScrollRef.current?.scrollTo({
      x: Math.max(0, offset),
      animated: true,
    });
  };

  useEffect(() => {
    scrollToCenter(selectedImage);
  }, [selectedImage]);

  if (isLoading) {
    return (
      <Container>
        <Header title={t("product_details")} showBack onBackPress={goBack} />
        <LoadingContainer>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </LoadingContainer>
      </Container>
    );
  }

  if (!product) {
    return (
      <Container>
        <Header title={t("product_details")} showBack onBackPress={goBack} />
        <ErrorContainer>
          <ErrorText>{t("product_not_found")}</ErrorText>
        </ErrorContainer>
      </Container>
    );
  }

  const handleAddToCart = async () => {
    if (!product) return;
    try {
      setIsAddedToCart(true);
      const res = await dispatch(
        addToCartAction({ product_id: product.product_id, quantity })
      ).unwrap();
      Toast.show({
        type: "success",
        text1: res.message || t("added_to_cart"),
      });
      router.push("/cart");
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || t("error_adding_to_cart"),
      });
    } finally {
      setIsAddedToCart(false);
    }
  };

  const handleQuantityChange = (value: number) => {
    if (!product) return;
    if (value > 0 && value <= product.quantity) {
      setQuantity(value);
    }
  };
  const handleDocumentPress = async (document: DocumentFile) => {
    if (!document) return;
    try {
      await WebBrowser.openBrowserAsync(document?.original_url);
    } catch (error) {
      Toast.show({
        type: "error",
        text1: t("document_download_error"),
      });
    }
  };

  const allImages = product.main_image
    ? [product.main_image, ...(product.gallery || [])]
    : [];

  return (
    <Container>
      <Header title={t("product_details")} showBack onBackPress={goBack} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <ImageContainer>
          <Carousel
            ref={carouselRef}
            loop={false}
            width={width}
            height={300}
            data={allImages}
            scrollAnimationDuration={500}
            onSnapToItem={(index) => setSelectedImage(index)}
            renderItem={({ item }) => (
              <ProductImage
                source={{ uri: item?.url || "" }}
                resizeMode="contain"
              />
            )}
          />
        </ImageContainer>
        {allImages.length > 0 && (
          <GalleryContainer>
            <ScrollView
              ref={thumbnailScrollRef}
              horizontal
              showsHorizontalScrollIndicator={false}
            >
              {allImages.map((image, index) => (
                <Pressable
                  key={image.id || index}
                  onPress={() => {
                    setSelectedImage(index);
                    carouselRef.current?.scrollTo({ index, animated: true });
                    scrollToCenter(index);
                  }}
                >
                  <GalleryImage
                    source={{ uri: image.url }}
                    selected={selectedImage === index}
                  />
                </Pressable>
              ))}
            </ScrollView>
          </GalleryContainer>
        )}

        <Content>
          <StyledHeader>
            {product.title && <Title>{product.title}</Title>}
            {product.quantity === 0 && (
              <StockStatus inStock={product.quantity > 0}>
                <StockText> {t("product.out_of_stock")}</StockText>
              </StockStatus>
            )}
          </StyledHeader>

          {product.sort_description && (
            <ShortDescription>
              <ShortDescriptionText>
                {product.sort_description}
              </ShortDescriptionText>
            </ShortDescription>
          )}

          {product.price && (
            <PriceContainer>
              <PriceLabel>{t("product.price")}</PriceLabel>
              <PriceValue>
                ₹
                {isDealerPriceShown
                  ? Math.round(parseFloat(product.dealer_price))
                  : Math.round(parseFloat(product.price))}
              </PriceValue>
            </PriceContainer>
          )}

          {product.categories && product.categories.length > 0 && (
            <CategoryContainer>
              {product.categories.map((category) => (
                <CategoryChip key={category.id}>
                  <CategoryText>{category.title}</CategoryText>
                </CategoryChip>
              ))}
            </CategoryContainer>
          )}

          {product.description && (
            <DescriptionContainer>
              <SectionHeader>
                <Ionicons
                  name="document-text-outline"
                  size={20}
                  color={theme.colors.text}
                />
                <SectionTitle>{t("product.description")}</SectionTitle>
              </SectionHeader>
              <RenderHTML htmlContent={product.description} />
            </DescriptionContainer>
          )}

          {product.documents &&
            product.documents.length > 0 &&
            product.documents.map((document) => (
              <DocumentContainer>
                <SectionHeader>
                  <Ionicons
                    name="document-text-outline"
                    size={20}
                    color={theme.colors.text}
                  />
                  <SectionTitle>{t("product.documents")}</SectionTitle>
                </SectionHeader>
                <DocumentButton onPress={() => handleDocumentPress(document)}>
                  <Ionicons
                    name="document-outline"
                    size={20}
                    color={theme.colors.primary}
                  />
                  <DocumentText>{document.name}</DocumentText>
                </DocumentButton>
              </DocumentContainer>
            ))}
        </Content>
      </ScrollView>

      <BottomContainer>
        <QuantityContainer>
          <QuantityButton
            onPress={() => handleQuantityChange(quantity - 1)}
            disabled={quantity <= 1}
          >
            <Ionicons
              name="remove"
              size={20}
              color={quantity <= 1 ? theme.colors.gray : theme.colors.text}
            />
          </QuantityButton>
          <QuantityValue>{quantity}</QuantityValue>
          <QuantityButton
            onPress={() => handleQuantityChange(quantity + 1)}
            disabled={quantity >= product.quantity}
          >
            <Ionicons
              name="add"
              size={20}
              color={
                quantity >= product.quantity
                  ? theme.colors.gray
                  : theme.colors.text
              }
            />
          </QuantityButton>
        </QuantityContainer>

        <AddToCartButton
          loading={isAddedToCart}
          onPress={handleAddToCart}
          disabled={product.quantity === 0 || isAddedToCart}
          title={
            product.quantity === 0
              ? t("product.out_of_stock")
              : t("product.add_to_cart")
          }
          style={{ opacity: product.quantity === 0 || isAddedToCart ? 0.5 : 1 }}
        />
      </BottomContainer>
    </Container>
  );
};

export default ProductDetailScreen;
