import React from "react";
import { PriceText } from "@/components/atoms";
import { Container, LabelText } from "./styles";
import { useTheme } from "@/hooks/useTheme";
import { View } from "react-native";

interface PriceSummaryRowProps {
  label: string;
  amount: string | number;
  variant?: "normal" | "bold";
  showPercentage?: boolean;
  percentage?: string | number;
  hasDiscount?: boolean;
  discountAmount?: string | number;
}

/**
 * PriceSummaryRow - Molecule component for price breakdown rows
 * Displays label and amount with optional percentage
 */
const PriceSummaryRow: React.FC<PriceSummaryRowProps> = ({
  label,
  amount,
  variant = "normal",
  showPercentage = false,
  percentage,
  hasDiscount = false,
  discountAmount,
}) => {
  const formatLabel = () => {
    if (showPercentage && percentage) {
      return `${label} (${percentage}%)`;
    }
    return label;
  };
  const { theme } = useTheme();
  return (
    <Container>
      <LabelText variant={variant}>{formatLabel()}</LabelText>

      <View style={{ flexDirection: "row", gap: 4, alignItems: "center" }}>
        {hasDiscount ? (
          <>
            <PriceText
              amount={Number(amount) + Number(discountAmount || 0)}
              variant={variant === "bold" ? "bold" : "primary"}
              size="medium"
              style={{
                textDecorationLine: "line-through",
                color: theme.colors.gray,
                fontSize: 12,
              }}
            />
            <PriceText
              amount={amount}
              variant={variant === "bold" ? "bold" : "primary"}
              size="medium"
              style={{
                color: theme.colors.text,
              }}
            />
          </>
        ) : (
          <PriceText
            amount={amount}
            variant={variant === "bold" ? "bold" : "primary"}
            size="medium"
            style={{
              color: theme.colors.text,
            }}
          />
        )}
      </View>
    </Container>
  );
};

export default PriceSummaryRow;
