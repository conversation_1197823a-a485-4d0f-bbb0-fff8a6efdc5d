import { useEffect, useState } from "react";
import { useRouter } from "expo-router";
import { RootState, useAppSelector } from "@/store/store";
import { LoadingOverlay, LocationPermissionModal } from "@/components";
import { useLocation } from "@/hooks/useLocation";
import * as Location from "expo-location";
import CustomSplashScreen from "@/components/organisms/CustomSplashScreen";
import * as SplashScreen from "expo-splash-screen";

SplashScreen.preventAutoHideAsync();
let isFirstLaunch = true;

export default function IndexScreen() {
  const router = useRouter();
  const token = useAppSelector((state: RootState) => state.auth.token);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [hasNavigated, setHasNavigated] = useState(false);

  let locationPermission = null;
  try {
    locationPermission = useLocation();
  } catch (error) {
    console.warn("Location permission not available:", error);
  }

  useEffect(() => {
    if (isFirstLaunch) {
      setTimeout(async () => {
        await SplashScreen.hideAsync();
        isFirstLaunch = false;
        handleNavigation();
      }, 2000);
    } else {
      handleNavigation();
    }
  }, []);

  const handleNavigation = () => {
    if (hasNavigated) return;

    if (!token) {
      router.replace("/(auth)/intro");
    } else {
      router.replace("/(protected)/(tabs)/home");
    }
    setHasNavigated(true);
  };

  const handleLocationPermissionComplete = async () => {
    setShowLocationModal(false);
    handleNavigation();
  };

  if (isFirstLaunch) {
    return <CustomSplashScreen />;
  }

  return (
    <>
      <LoadingOverlay isLoading={true} size="large" />
      <LocationPermissionModal
        isVisible={showLocationModal}
        onComplete={handleLocationPermissionComplete}
      />
    </>
  );
}
