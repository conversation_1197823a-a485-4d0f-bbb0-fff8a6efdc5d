{"welcome": "Welcome Back", "intro": {"greeting": "Hello, glad to have you here!", "question": "Before we start, we need to ask:\n Are you a Dealer or a Customer?", "dealer_button": "I'M A DEALER", "customer_button": "I'M A CUSTOMER", "have_account": "I already have an account"}, "enter_phone": "Enter your phone number to continue", "continue": "Continue", "next": "Next", "dont_have_account": "Don't have an account? Register", "verify_otp": "Verify OTP", "enter_otp": "Enter the 6-digit code sent to {{phone}}", "verify": "Verify", "resend_code": "Didn't receive the code?", "invalid_otp": "Invalid OTP. Please try again.", "dashboard": "Dashboard", "profile": {"title": "Profile", "editTitle": "Edit Profile", "personalInfo": "Personal Information", "addressInfo": "Address Information", "firstName": "First Name", "lastName": "Last Name", "name": "Full Name", "email": "Email", "mobile": "Mobile Number", "address": "Address", "pincode": "Pincode", "gstNumber": "GST Number", "notSet": "Not set", "saveChanges": "Save Changes", "editProfile": "Edit Profile", "enterFirstName": "Enter your first name", "enterLastName": "Enter your last name", "enterName": "Enter your full name", "enterEmail": "Enter your email", "enterMobile": "Enter your mobile number", "enterAddressLineOne": "Enter address line one", "enterAddressLineTwo": "Enter address line two", "enterPincode": "Enter pincode", "enterGstNumber": "Enter GST number", "companyName": "Company Name", "city": "City", "state": "State", "country": "Country", "addressLineOne": "Address Line One", "addressLineTwo": "Address Line Two"}, "products": "Products", "settings": {"title": "Settings", "languageChangeSuccess": "Language changed successfully"}, "language": "Language", "language_selection": "Select Language", "english": "English", "hindi": "Hindi", "gujarati": "Gujarati", "appearance": "Appearance", "dark_mode": "Dark Mode", "logout": {"title": "Logout", "message": "Are you sure you want to logout?", "confirm": "Yes, <PERSON><PERSON><PERSON>", "cancel": "Cancel", "error": "Failed to logout. Please try again."}, "register": "Register", "create_account": "Create Account", "fill_details": "Fill in your details to get started", "fill_profile_details": "Fill in your profile details to complete vendor profile", "fill_address_details": "Fill in your address details to complete vendor address", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "phone_number": "Phone Number", "enter_first_name": "Enter your first name", "enter_last_name": "Enter your last name", "enter_email": "Enter your email address", "enter_address": "Enter your address", "enter_address_two": "Enter your address line two (optional)", "already_have_account": "Already have an account?", "error": {"title": "Error"}, "first_name_required": "First name is required", "last_name_required": "Last name is required", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "invalid_phone": "Please enter a valid 10-digit phone number", "country": "Country", "select_country": "Select Country", "state": "State", "select_state": "Select State", "city": "City", "select_city": "Select City", "address": "Address", "complete_profile": "Complete Profile", "address_line_one": "Address Line One", "address_line_two": "Address Line Two", "post_code": "Post Code", "enter_postal_code": "Enter postal code", "gstin": "GSTIN Number", "enter_gst_number": "Enter GST number", "authorized_dealer": "Authorized Dealer", "pan_number": "PAN Number", "permission_needed": "Permission Needed", "photo_permission_message": "Please grant permission to access your photos", "upload_profile_picture": "Please upload a profile picture", "profile_updated": "Profile updated successfully", "profile_update_failed": "Failed to update profile", "saving": "Saving...", "save_profile": "Save Profile", "tap_to_add_photo": "Tap to add photo", "product_details": "Product Details", "buy_now": "Buy Now", "removed_from_wishlist": "Removed from Wishlist", "added_to_wishlist": "Added to Wishlist", "added_to_cart": "Added to <PERSON><PERSON>", "product_not_found": "Product not found", "quantity": "Quantity", "contact_person_name": "Contact Person Name", "company_name": "Company Name", "gst_number": "GST Number", "enter_address_line_one": "Enter address line one", "enter_address_line_two": "Enter address line two", "enter_post_code": "Enter post code", "enter_contact_person_name": "Enter contact person name", "enter_company_name": "Enter company name", "enter_phone_number": "Enter phone number", "address_type": "Address Type", "billing": "Billing", "shipping": "Shipping", "update_address": "Update Address", "add_address": "Add Address", "cart": {"title": "<PERSON><PERSON>", "empty": "Your cart is empty", "start_shopping": "Start Shopping", "clear": "Clear Cart", "continue": "Continue", "checkout": "Checkout", "total": "Total", "subtotal": "Subtotal", "cgst": "CGST", "sgst": "SGST", "igst": "IGST", "place_order": "Place Order", "clearTitle": "Clear Cart", "clearMessage": "Are you sure you want to remove all items from your cart?", "clearError": "Failed to clear cart", "selectVendor": "Select Vendor", "searchVendor": "Search vendors", "selectedVendor": "Selected Vendor", "vendorId": "Vendor ID", "vendorEmail": "Email", "steps": {"cart": "<PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "address": "Address", "payment": "Payment", "summary": "Summary"}, "payment": {"title": "Payment Information", "method": "Payment Method", "proof": "Payment Proof", "proof_uploaded": "Proof uploaded", "proof_required": "Proof required", "proof_image": "Payment Proof Image", "methods": {"bank_account": "Bank Account", "bank_account_desc": "Transfer to bank account", "upi_id": "UPI ID", "upi_id_desc": "Pay using UPI", "qr_code": "QR Code", "qr_code_desc": "Scan QR code to pay", "credit": "Credit", "credit_desc": "Pay on credit", "unknown": "Unknown"}, "details": {"account_number": "Account Number:", "ifsc_code": "IFSC Code:", "upi_id": "UPI ID:", "qr_code": "QR Code:", "credit_selected": "Credit payment option selected"}, "errors": {"select_method": "Please select a payment method", "upload_proof": "Please upload payment proof to continue"}, "success": {"proof_uploaded": "Payment proof uploaded successfully!"}, "buttons": {"back": "Back", "continue": "Continue", "place_order": "Place Order", "placing_order": "Placing Order..."}}}, "wishlist": "Wishlist", "wishlist_empty": "Your wishlist is empty", "start_shopping": "Start Shopping", "total": "Total", "clear_cart": "Clear Cart", "order_now": "Order Now", "items": "items", "price": "Price", "notifications": "Notifications", "push_notifications": "Push Notifications", "email_notifications": "Email Notifications", "whatsapp_notifications": "WhatsApp Notifications", "notification_permission_failed": "Failed to get push token for push notification!", "close": "Close", "orders": "Orders", "active_orders": "Active Orders", "past_orders": "Past Orders", "manage_orders": "Manage Orders", "order_history": "Order History", "order_status": "Order Status", "view_details": "View Details", "cancel_order": "Cancel Order", "processing": "Processing", "delivered": "Delivered", "complaints": "<PERSON><PERSON><PERSON><PERSON>", "active_complaints": "Active Complaints", "past_complaints": "Past Complaints", "complaint_history": "Complaint History", "track_status": "Track Status", "under_review": "Under Review", "resolved": "Resolved", "raise_complaint": "<PERSON><PERSON>", "welcome_back": "Welcome Back", "dont_have_an_account": "Don't have an account? ", "register_here": "Register here", "submit": "Submit", "back": "Back", "otp_verification": "OTP Verification", "did_not_receive_otp": "Didn't receive the OTP?", "resend": "Resend", "verify_now": "Verify Now", "select_country_code": "Select Country Code", "search_country_code": "Search country code", "search_country": "Search country", "search_state": "Search state", "search_city": "Search city", "complaint": {"title": "<PERSON><PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON><PERSON>", "id": "Complaint ID", "status": "Status", "description": "Description", "serial_number": "Serial Number", "created_at": "Created At", "resolution": "Resolution", "enter_serial_number": "Enter serial number", "enter_complaint_description": "Enter complaint description", "not_found": "<PERSON><PERSON><PERSON><PERSON> not found", "error_loading": "Error loading complaint", "error_loading_list": "Error loading complaints", "no_complaints": "No complaints found", "loading": "Loading...", "raise": "<PERSON><PERSON>", "enter_description": "Enter complaint description", "upload_image": "Upload Image", "tap_to_add_photo": "Tap to add photo", "add_more_photos": "Add more photos", "submit": "Submit <PERSON><PERSON><PERSON><PERSON>", "warranty_status": "Warranty Status", "repair_cost": "Repair Cost", "update_status": "Update Status", "new_complaint": "New Complaint", "complaint_for": "Choose complaint for", "select_complaint_type": "Select complaint type", "select_or_add": "Select from list or add manually", "search_vendor_customer": "Search vendor/customer", "selected_vendor": "Selected Vendor", "selected_customer": "Selected Customer", "add_manually": "Add manually", "enter_mobile_number": "Enter mobile number (for WhatsApp updates)", "battery_brand": "Select Battery Brand", "select_battery_brand": "Select battery brand", "qr_code": "QR Code", "enter_qr_code": "Enter QR code", "product_image": "Product Image", "brand_name": "Brand Name", "enter_brand_name": "Enter brand name", "assign_service_person": "Assign Service Person", "steps": {"complaint_details": "<PERSON><PERSON><PERSON><PERSON>", "product_details": "Product Details", "address": "Address"}, "types": {"vendor": "<PERSON><PERSON><PERSON>", "customer": "Customer", "vendor_desc": "For business partners", "customer_desc": "For end customers"}, "brands": {"ozone": "Ozone Battery", "other": "Other Brand", "ozone_desc": "Ozone brand batteries", "other_desc": "Third-party brand batteries", "third_party": "3rd Party Battery"}, "form": {"choose_complaint_for": "Choose complaint for", "select_who_complaint_for": "Select who this complaint is for", "select_vendor": "Select vendor", "select_customer": "Select customer", "search_existing_add_manually": "Search existing or add manually", "change_selection": "Change selection", "tap_to_change": "Tap to change selection", "mobile_number": "Mobile Number", "enter_mobile_number": "Enter mobile number", "enter_first_name": "Enter first name", "enter_last_name": "Enter last name", "add_vendor_manually": "Add Vendor Manually", "add_customer_manually": "Add Customer Manually", "select_battery_brand": "Select Battery Brand", "choose_battery_brand": "Choose between Ozone battery or other brand", "select_issue": "Select Issue", "choose_issue_type": "Choose the type of issue you're experiencing", "describe_issue": "Describe Issue", "describe_your_issue_placeholder": "Please describe your specific issue...", "additional_details": "Additional Details", "add_more_details_optional": "Add any additional details (optional)", "add_additional_details_placeholder": "Add any additional information about the issue...", "select_address": "Select Address", "choose_complaint_address": "Choose the address for this complaint", "choose_complaint_address_for_entity": "Choose the address for {{entity}}", "no_addresses_found": "No addresses found", "no_addresses_found_for_entity": "No addresses found for {{entity}}", "current_user": "Current User", "tap_to_select_issue": "Tap to select an issue", "ozone_battery_details": "Ozone Battery Details", "scan_qr_enter_serial": "Scan QR code or enter serial number", "scan_qr_code": "Scan QR Code", "enter_qr_code_manually": "Enter QR code manually", "enter_battery_serial": "Enter battery serial number", "other_brand_details": "Other Brand Details", "upload_image_enter_brand": "Upload product image and enter brand details", "product_image_required": "Product Image", "brand_name_required": "Brand Name", "enter_brand_name_placeholder": "Enter brand name", "complaint_description": "Complaint Description", "describe_issue_detail": "Describe the issue in detail", "describe_problem_placeholder": "Describe the problem with the battery..."}, "navigation": {"previous": "Previous", "next": "Next", "back": "Back"}, "validation": {"select_complaint_type": "Please select complaint type", "select_vendor_customer_or_mobile": "Please select a vendor/customer or add manually with mobile number", "provide_serial_or_qr": "Please provide either serial number or QR code for Ozone battery", "product_image_required": "Product image is required for other brand batteries", "brand_name_required": "Brand name is required for other brand batteries", "mobile_number_required_for_manual_entry": "Mobile number is required when adding manually", "first_name_required": "First name is required when adding manually", "last_name_required": "Last name is required when adding manually", "select_issue": "Please select an issue type", "custom_description_required": "Please describe your issue when selecting 'Other'", "billing_address_required": "Billing address is required", "shipping_address_required": "Shipping address is required when not using billing address"}, "errors": {"max_images_reached": "Maximum images reached", "max_images_message": "You can only upload up to 5 images"}, "qr_scanner": {"title": "QR Scanner", "message": "QR code scanning will be implemented here"}, "submitted_successfully": "<PERSON><PERSON><PERSON><PERSON> Submitted", "submitted_successfully_desc": "Your complaint has been submitted successfully", "submission_failed": "Failed to submit complaint", "user_specific": {"vendor_flow": "<PERSON><PERSON><PERSON>", "customer_flow": "Customer Co<PERSON><PERSON>t Flow", "salesperson_flow": "Salesperson <PERSON><PERSON><PERSON><PERSON>", "vendor_complaint_for_customer": "Complaint for Customer", "customer_complaint_for_vendor": "<PERSON>mplaint for Vendor", "salesperson_complaint_choice": "Choose complaint type"}, "address_validation": {"no_addresses": "No addresses found", "billing_required": "Billing address is required", "shipping_required": "Shipping address is required when not using billing address"}, "location": "Location", "created_by": "Created By", "type": "Type", "issue": "Issue", "product_photos": "Product Photos", "issue_or_description_required": "Please provide either an issue or description", "product_info": "Product Information", "addresses": "Addresses", "history": "History", "billing_address_id": "Billing Address ID", "shipping_address_id": "Shipping Address ID", "coordinates": "Coordinates", "complaint_number": "Complaint Number", "updated_at": "Updated At", "status_changed_to": "Status changed to", "selected_service_person": "Selected Service Person", "estimation": {"title": "Estimation Details", "product": "Product", "quantity": "Quantity", "estimate_time": "Estimated Time", "created_at": "Estimation Date", "not_found": "Estimation details not found"}, "status_change_success": "Status updated successfully", "estimation_sent_success": "Estimation sent successfully"}, "estimation": {"send_estimation_details": "Send Estimation Details", "slug": "Slug", "enter_slug": "Enter slug", "estimate_time": "Estimate Time", "enter_estimate_time": "Enter estimate time (e.g., 2-3 days)", "product": "Product", "enter_product": "Enter product name", "quantity": "Quantity", "enter_quantity": "Enter quantity", "submit": "Submit", "submiting": "Submitting...", "slug_required": "Slug is required", "estimate_time_required": "Estimate time is required", "product_required": "Product is required", "quantity_required": "Quantity is required", "quantity_numeric": "Quantity must be numeric"}, "order": {"title": "Order", "details": "Order Details", "id": "Order ID", "status": "Status", "products": "Products", "not_found": "Order not found", "error_loading": "Error loading order", "error_loading_list": "Error loading orders", "no_orders": "No orders found", "loading": "Loading...", "track": "Track Order", "cancel": "Cancel Order", "cancel_confirm": "Are you sure you want to cancel this order?", "cancel_success": "Order cancelled successfully", "cancel_error": "Failed to cancel order", "total": "Total", "subtotal": "Subtotal", "taxes": "Taxes", "discount": "Discount", "payment_status": "Payment Status", "paid": "Paid", "unpaid": "Unpaid", "type": "Order Type", "addresses": "Addresses", "same_as_billing": "Same as billing address", "pricing_details": "Pricing Details", "tracking_number": "Tracking Number", "timeline": "Order Timeline", "view_proforma": "View Proforma Invoice", "proforma_error": "Failed to open proforma invoice. Please try again.", "direct": "Direct", "complient": "Complient", "ordertype": "Order Type", "approve": "Approve", "reject": "Reject", "reject_order": "Reject Order", "rejection_reason": "Rejection Reason", "rejection_reason_placeholder": "Please provide a reason for rejecting this order...", "rejection_reason_required": "Rejection reason is required", "approving": "Approving...", "rejecting": "Rejecting...", "approved_successfully": "Order approved successfully", "rejected_successfully": "Order rejected successfully", "approval_failed": "Failed to approve order", "rejection_failed": "Failed to reject order", "dispatch": "Dispatch", "dispatching": "Dispatching...", "dispatch_order": "Dispatch Order", "dispatched_successfully": "Order dispatched successfully", "dispatch_failed": "Failed to dispatch order", "move_to_in_dispatch": "Move to In Dispatch", "moving_to_in_dispatch": "Moving to In Dispatch...", "mark_as_received": "<PERSON> as Received", "marking_as_received": "Marking as Received...", "vehicle_number": "Vehicle Number", "vehicle_number_required": "Vehicle number is required", "enter_vehicle_number": "Enter vehicle number", "tracking_id": "Tracking ID", "tracking_id_required": "Tracking ID is required", "enter_tracking_id": "Enter tracking ID", "tracking_url": "Tracking URL", "enter_tracking_url": "Enter tracking URL", "notes": "Notes", "enter_notes": "Enter Notes", "courier_name": "Courier Name", "courier_name_required": "Courier name is required", "enter_courier_name": "Enter courier name"}, "status": {"pending": "Pending", "in_review": "In Review", "estimation_sent": "Estimation Sent", "approved": "Approved", "repair": "Repair", "in_progress": "In Progress", "resolved": "Resolved", "rejected": "Rejected", "closed": "Closed", "1": "Pending", "2": "In Review", "3": "Estimation Sent", "4": "Approved", "5": "Repair In Progress", "6": "Resolved", "7": "Rejected", "8": "Assignee"}, "warranty": {"in_warranty": "In Warranty", "out_of_warranty": "Out of Warranty", "partial_warranty": "Partial Warranty"}, "add_vendor": "Add <PERSON>", "edit_vendor": "Update Vendor", "send_otp": "Send OTP", "adding_vendor": "Adding Vendor...", "add_vendor_details": "Add vendor details to get started", "error_updating_status": "Failed to update complaint status", "vendorManagement": {"title": "Vendor Management", "manageVendors": "Manage Vendors", "manageVendorsSubtitle": "View and manage your vendor network", "searchPlaceholder": "Search vendors by name", "noVendors": "No vendors found. Add your first vendor to get started.", "noSearchResults": "No vendors match your search criteria.", "tryDifferentSearch": "Try a different search term or add a new vendor.", "addFirstVendor": "Start building your vendor network by adding your first vendor.", "loadError": "Failed to load vendors. Please try again.", "deleteTitle": "Delete Vendor", "deleteMessage": "Are you sure you want to delete {{name}}? This action cannot be undone.", "deleteSuccess": "<PERSON><PERSON><PERSON> deleted successfully", "deleteError": "Failed to delete vendor. Please try again.", "editNotImplemented": "Edit functionality will be available soon", "gstNumber": "GST Number", "address": "Address", "country": "Country", "authDealer": "Authorized Dealer", "authorized": "Authorized", "unauthorized": "Unauthorized", "totalVendors": "Total Vendors", "total": "Total", "vendors": "vendors"}, "common": {"error_message": "Something went wrong, please try again later", "something_went_wrong": "Something went wrong", "no_products_found": "No products found", "otp_verified_success": "OTP verified successfully", "search": "Search", "search_product": "Search product", "search_vendor": "Search vendor", "search_order": "Search order", "search_complaint": "Search complaint", "search_placeholder": "Type to search...", "sort_by": "Sort by", "sort": "Sort", "sort_price_low_to_high": "Price: Low to High", "sort_price_high_to_low": "Price: High to Low", "sort_name_a_to_z": "Name: A to Z", "sort_name_z_to_a": "Name: Z to A", "select": "Select", "default": "default", "cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "back": "Back", "delete": "Delete", "edit": "Edit", "no_results_found": "No results found", "no_vendors_available": "No vendors available", "not_available": "N/A", "search_customers": "Search customers", "no_customers_available": "No customers available", "no_camera_access": "No access to camera", "no_gallery_access": "No access to gallery", "select_country_code": "Select Country Code", "search_country": "Search country", "loading": "Loading...", "first_name": "First Name", "last_name": "Last Name", "retry": "Retry", "change": "Change", "remove": "Remove", "name": "Name", "email": "Email"}, "product": {"search_products": "Search products", "out_of_stock": "Out of Stock", "price": "Price", "description": "Description", "add_to_cart": "Add to Cart", "documents": "Documents"}, "location": {"noLocationsFound": "No locations found"}, "filter": "Filter", "filter.reset": "Reset", "filter.apply": "Apply", "clear.all": "Clear All", "filter.price_range": "Price Range", "sort": "Sort", "validation": {"first_name_required": "First name is required", "first_name_format": "First name must be 2-50 characters and contain only letters", "last_name_required": "Last name is required", "last_name_format": "Last name must be 2-50 characters and contain only letters", "phone_required": "Phone number is required", "serial_or_image_required": "Please provide either a serial number or upload an image", "description_required": "Description is required", "vendorRequired": "Please select a vendor to continue", "mobile_required": "Mobile number is required for WhatsApp updates", "mobile_format": "Mobile number must be 10 digits", "serial_or_qr_required": "Serial number or QR code is required for own-brand batteries", "product_image_required": "Product image is required for 3rd-party brand batteries", "brand_name_required": "Brand name is required for 3rd-party brand batteries", "required": "Required", "mobile_number_required_for_manual_entry": "Mobile number is required when adding manually", "error": "Validation Error"}, "login": "<PERSON><PERSON>", "currency": {"symbol": "₹"}, "addressSelection": {"title": "Your Addresses", "subtitle": "Choose from your saved addresses or add a new one", "addNewAddress": "Add New Address", "useBillingAsShipping": "Use billing address as shipping address", "billingAddresses": "Billing Addresses", "shippingAddresses": "Shipping Addresses", "shippingSameAsBilling": "Shipping address same as billing address", "noAddressesFound": "No addresses found", "noAddressesDescription": "Add your first address to continue with checkout", "search_placeholder": "Search your addresses", "noShippingAddresses": "No shipping addresses found. Please add a shipping address to continue."}}