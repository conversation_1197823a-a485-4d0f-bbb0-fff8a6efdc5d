import React, { useState, useEffect } from "react";
import { useR<PERSON>er, useLocalSearchParams } from "expo-router";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { CountryCode } from "@/types/auth";
import { AddVendorBasicForm, type AddVendorBasicFormData } from "@/components";
import FormTemplate from "@/template/FormTemplate";

import {
  ContentContainer,
  StepperContainer,
  Form,
} from "@/styles/AddVendor.styles";
import { useTheme } from "@/hooks/useTheme";
import { Header, Stepper } from "@/components";
import { KeyboardAvoidingView } from "react-native";
import { AddVendorStep, UserType } from "@/types/api";
import { Redirect } from "expo-router";
import Toast from "react-native-toast-message";
import {
  setVendor,
  updateVendor,
  resetVendorForm,
} from "@/store/slices/vendorSlice";
import {
  getVendorListAction,
  vendorInsertUpdateAction,
} from "@/store/actions/vendor";
import AddVendorAddressFormForm, {
  AddVendorAddressFormFormData,
} from "@/components/organisms/AddVendorAddressForm";
const NAME_REGEX = /^[a-zA-Z]{2,50}$/;
const PHONE_REGEX = /^[0-9]{10}$/;
const GSTIN_REGEX = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][A-Z0-9]Z[A-Z0-9]$/;

export const stepData = [
  { id: 1, label: "Basic", name: AddVendorStep.BASICINFO },
  { id: 2, label: "Adderss", name: AddVendorStep.ADDRESS },
];

const registerSchema = yup.object().shape({
  firstName: yup
    .string()
    .required("First name is required")
    .transform((value) => value?.trim())
    .matches(
      NAME_REGEX,
      "First name must be 2-50 characters and contain only letters"
    ),
  lastName: yup
    .string()
    .required("Last name is required")
    .transform((value) => value?.trim())
    .matches(
      NAME_REGEX,
      "Last name must be 2-50 characters and contain only letters"
    ),
  email: yup.string().email("Invalid email format").notRequired(),
  mobile: yup
    .number()
    .required("Phone number is required")
    .test("is-valid-phone", "Invalid phone number format", (value) => {
      if (value === null || value === undefined) return false;
      return PHONE_REGEX.test(value.toString());
    }),
  gst_number: yup
    .string()
    .required("GST number is required")
    .matches(GSTIN_REGEX, "Invalid GST number format"),
  auth_dealer: yup.boolean().required("Auth dealer is required"),
});

const addressSchema = yup.object().shape({
  address_line_one: yup
    .string()
    .transform((val) => val?.trim() || "")
    .test(
      "len",
      "Address line one must be between 2 and 100 characters",
      (val) => !val || (val.length >= 2 && val.length <= 100)
    ),
  address_line_two: yup
    .string()
    .transform((val) => val?.trim() || "")
    .test(
      "len",
      "Address line two must be between 2 and 100 characters",
      (val) => !val || (val.length >= 2 && val.length <= 100)
    ),
  post_code: yup
    .string()
    .transform((val) => val?.trim() || "")
    .test(
      "is-numeric",
      "Must enter postcode in numeric value",
      (val) => !val || /^\d+$/.test(val)
    )
    .test(
      "len",
      "Post code must be 6 digits",
      (val) => !val || val.length === 6
    ),
  state_id: yup
    .number()
    .nullable()
    .transform((val) => (val === "" ? null : val)),
  city_id: yup
    .number()
    .nullable()
    .transform((val) => (val === "" ? null : val)),
});

export default function RegisterScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const params = useLocalSearchParams();
  const { countryList } = useAppSelector((state: RootState) => state.auth);

  const { vendor, vendorList } = useAppSelector(
    (state: RootState) => state.vendor
  );
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTabNav, setSelectedTabNav] = useState(AddVendorStep.BASICINFO);
  const { theme } = useTheme();

  // Check if we're in edit mode by presence of vendorId parameter
  const vendorId = params.vendorId ? parseInt(params.vendorId as string) : null;
  const isEditMode = !!params.vendorId;

  // Find vendor data from Redux state using vendor ID
  const editVendorData =
    isEditMode && vendorId
      ? vendorList?.find((v) => v.vendor_id === vendorId)
      : null;
  const [countryCode, setCountryCode] = useState<CountryCode | null>(
    countryList?.find(
      (c) =>
        c.country_code_alpha ===
        (vendor?.country_code_alpha ||
          (isEditMode ? editVendorData?.country_code_alpha : null))
    ) ||
      countryList?.[0] ||
      null
  );

  const [selectedCountryId, setSelectedCountryId] = useState<number | null>(
    countryCode?.id || null
  );

  useEffect(() => {
    if (!isEditMode) {
      return () => {
        dispatch(resetVendorForm());
      };
    }
  }, [isEditMode, dispatch]);

  const handleCountrySelect = (item: CountryCode) => {
    setCountryCode(item);
    setSelectedCountryId(item.id);
  };

  const getStepValidation = (stepId: number) => {
    switch (stepId) {
      case AddVendorStep.BASICINFO:
        return true;
      case AddVendorStep.ADDRESS:
        return true;
      default:
        return false;
    }
  };

  const onSubmitBasicInfo = (data: AddVendorBasicFormData) => {
    dispatch(
      setVendor({
        ...vendor, // Preserve existing data including address
        country_code_alpha: countryCode?.country_code_alpha,
        mobile: data.mobile,
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        gst_number: data.gst_number,
        auth_dealer: data.auth_dealer ? "1" : "0",
      })
    );
    setSelectedTabNav(AddVendorStep.ADDRESS);
  };

  const onSubmitProfile = async (data: AddVendorAddressFormFormData) => {
    try {
      setIsLoading(true);
      const payload = {
        ...(isEditMode &&
          editVendorData?.vendor_id && { vendor_id: editVendorData.vendor_id }), // Include ID for updates
        first_name: vendor?.first_name,
        last_name: vendor?.last_name,
        mobile: vendor?.mobile,
        email: vendor?.email,
        gst_number: vendor?.gst_number,
        auth_dealer: vendor?.auth_dealer,
        address_line_one: data.address_line_one,
        address_line_two: data.address_line_two,
        post_code: data.post_code,
        state_id: data.state_id,
        city_id: data.city_id,
        country_code_alpha: countryCode?.country_code_alpha,
      };
      const res = await dispatch(vendorInsertUpdateAction(payload)).unwrap();
      Toast.show({
        type: "success",
        text1: res.message,
      });
      if (res.status) {
        await dispatch(getVendorListAction({})).unwrap();
        router.back();
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToBasicInfo = (
    addressData?: AddVendorAddressFormFormData
  ) => {
    if (addressData) {
      dispatch(
        updateVendor({
          address_line_one: addressData.address_line_one,
          address_line_two: addressData.address_line_two,
          post_code: addressData.post_code,
          state_id: addressData.state_id,
          city_id: addressData.city_id,
        })
      );
    }
    setSelectedTabNav(AddVendorStep.BASICINFO);
  };

  const renderForm = () => {
    switch (selectedTabNav) {
      case AddVendorStep.BASICINFO:
        return (
          <FormTemplate<AddVendorBasicFormData>
            key={`basic-form-${selectedTabNav}-${vendor?.first_name}-${vendor?.last_name}-${vendor?.mobile}-${vendor?.email}-${vendor?.gst_number}-${vendor?.auth_dealer}`}
            Component={(props) => (
              <AddVendorBasicForm
                {...props}
                countryCode={countryCode}
                onCountrySelect={handleCountrySelect}
              />
            )}
            onSubmit={onSubmitBasicInfo}
            defaultValues={{
              firstName:
                vendor?.first_name ||
                (isEditMode ? editVendorData?.first_name : "") ||
                "",
              lastName:
                vendor?.last_name ||
                (isEditMode ? editVendorData?.last_name : "") ||
                "",
              mobile:
                vendor?.mobile ??
                (isEditMode ? editVendorData?.mobile ?? 0 : 0),
              email:
                vendor?.email ||
                (isEditMode ? editVendorData?.email : "") ||
                "",
              gst_number:
                vendor?.gst_number ||
                (isEditMode ? editVendorData?.gst_number : "") ||
                (__DEV__ ? "27ABCDE1234F1Z5" : ""),
              auth_dealer:
                vendor?.auth_dealer !== undefined
                  ? vendor.auth_dealer === "1"
                  : isEditMode
                  ? editVendorData?.auth_dealer === "1"
                  : false,
            }}
            resolver={yupResolver(registerSchema)}
            mode="all"
          />
        );
      case AddVendorStep.ADDRESS:
        return (
          <FormTemplate<AddVendorAddressFormFormData>
            key={`address-form-${selectedTabNav}-${vendor?.address_line_one}-${vendor?.address_line_two}-${vendor?.post_code}-${vendor?.state_id}-${vendor?.city_id}`}
            Component={(props) => (
              <AddVendorAddressFormForm
                {...props}
                onBack={handleBackToBasicInfo}
                selectedCountryId={selectedCountryId}
                loading={isLoading}
              />
            )}
            onSubmit={onSubmitProfile}
            defaultValues={{
              address_line_one:
                vendor?.address_line_one ||
                (isEditMode ? editVendorData?.address_line_one : "") ||
                "",
              address_line_two:
                vendor?.address_line_two ||
                (isEditMode ? editVendorData?.address_line_two : "") ||
                "",
              post_code:
                vendor?.post_code ||
                (isEditMode ? editVendorData?.post_code : "") ||
                "",
              state_id:
                vendor?.state_id ||
                (isEditMode ? editVendorData?.state_id : null) ||
                null,
              city_id:
                vendor?.city_id ||
                (isEditMode ? editVendorData?.city_id : null) ||
                null,
            }}
            resolver={yupResolver(addressSchema)}
            mode="all"
          />
        );

      default:
        return null;
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: theme.colors.background }}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <Header
        title={isEditMode ? t("edit_vendor") : t("add_vendor")}
        showCart={false}
        showBack={true}
      />
      <ContentContainer>
        <StepperContainer>
          <Stepper
            stepData={stepData}
            currentId={selectedTabNav}
            setSelectedTabNav={setSelectedTabNav}
            allowFutureStepNavigation={true}
            getStepValidation={getStepValidation}
          />
        </StepperContainer>
        <Form>{renderForm()}</Form>
      </ContentContainer>
    </KeyboardAvoidingView>
  );
}
