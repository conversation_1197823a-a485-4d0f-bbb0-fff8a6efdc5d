import React, { Fragment, useState } from "react";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { OrderStatusEnum, OrderStatus, OrderPaid } from "@/types/order";
import { UserType } from "@/types/api";
import { SectionHeader } from "@/components/atoms";
import { useAppSelector } from "@/store/store";
import { DispatchModal } from "../DispatchModal";
import RejectModal, { RejectData } from "../RejectModal";
import {
  Container,
  TimelineContainer,
  TimelineItem,
  TimelineIndicator,
  TimelineLine,
  TimelineContent,
  TimelineTitle,
  ApprovalButtonsContainer,
  ApprovalButton,
  ApprovalButtonText,
} from "./styles";
import Toast from "react-native-toast-message";

interface OrderTimelineProps {
  currentStatus: OrderStatusEnum;
  orderStatusList: OrderStatus[];
  orderId?: number;
  onApprove?: (orderId: number) => Promise<void>;
  onReject?: (orderId: number, reason?: string) => Promise<void>;
  onDispatch?: (orderId: number, dispatchData: DispatchData) => Promise<void>;
}

export interface DispatchData {
  vehical_no: string;
  tracking: string;
  tracking_url: string;
  notes: string;
  courier_name: string;
}

const OrderTimeline: React.FC<OrderTimelineProps> = ({
  currentStatus,
  orderStatusList,
  orderId,
  onApprove,
  onReject,
  onDispatch,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [showDispatchModal, setShowDispatchModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [isDispatching, setIsDispatching] = useState(false);
  const [isMarkingAsReceived, setIsMarkingAsReceived] = useState(false);
  const { user } = useAppSelector((state) => state.auth);
  const { orderDetails } = useAppSelector((state) => state.orders);
  const isVendor = user?.role_id === UserType.VENDOR;
  const isSalesperson = user?.role_id === UserType.SALESPERSON;
  const currentOrder = orderDetails;
  const getStepStatus = (stepId: number) => {
    if (Number(currentStatus) === OrderStatusEnum.Cancelled) {
      if (stepId === OrderStatusEnum.Cancelled) {
        return { isCompleted: false, isActive: true };
      } else if (stepId === OrderStatusEnum.Pending) {
        return { isCompleted: true, isActive: false };
      } else {
        return { isCompleted: false, isActive: false };
      }
    }
    const isCompleted = stepId < Number(currentStatus);
    const isActive = stepId === Number(currentStatus);
    return { isCompleted, isActive };
  };

  const handleApprove = async () => {
    if (!orderId || !onApprove) return;

    try {
      setIsApproving(true);
      await onApprove(orderId);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to approve order",
      });
    } finally {
      setIsApproving(false);
    }
  };

  const handleReject = () => {
    setShowRejectModal(true);
  };

  const handleRejectConfirm = async (rejectData: RejectData) => {
    if (!orderId || !onReject) return;

    try {
      setIsRejecting(true);
      await onReject(orderId, rejectData.reason);
      setShowRejectModal(false);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to reject order",
      });
    } finally {
      setIsRejecting(false);
    }
  };

  const handleRejectCancel = () => {
    setShowRejectModal(false);
  };

  const shouldShowApprovalButtons = (stepId: number) => {
    return (
      isVendor &&
      stepId === OrderStatusEnum.Pending &&
      currentStatus === OrderStatusEnum.Pending &&
      orderId &&
      onApprove &&
      onReject &&
      orderDetails.created_by_id
    );
  };

  // Todo: temporary disabled now admin panel will be status changed

  // const shouldShowDispatchButton = (stepId: number) => {
  //   return (
  //     isSalesperson &&
  //     stepId === OrderStatusEnum.InDispatch &&
  //     currentStatus === OrderStatusEnum.InDispatch &&
  //     orderId &&
  //     onDispatch
  //   );
  // };

  // const shouldShowMoveToInDispatchButton = (stepId: number) => {
  //   const result =
  //     isSalesperson &&
  //     stepId === OrderStatusEnum.Approved &&
  //     currentStatus === OrderStatusEnum.Approved &&
  //     orderId &&
  //     !!onApprove;

  //   return result;
  // };

  const shouldShowMarkAsReceivedButton = (stepId: number) => {
    return (
      isVendor &&
      stepId === OrderStatusEnum.Dispatched &&
      currentStatus === OrderStatusEnum.Dispatched &&
      orderId &&
      onApprove
    );
  };

  const handleDispatch = () => {
    setShowDispatchModal(true);
  };

  const handleDispatchConfirm = async (dispatchData: DispatchData) => {
    if (!orderId || !onDispatch) return;
    try {
      setIsDispatching(true);
      await onDispatch(orderId, dispatchData);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to dispatch order",
      });
    } finally {
      setShowDispatchModal(false);
      setIsDispatching(false);
    }
  };

  const handleDispatchCancel = () => {
    setShowDispatchModal(false);
  };

  const handleMoveToInDispatch = async () => {
    if (!orderId || !onApprove) return;

    try {
      setIsApproving(true);
      await onApprove(orderId);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to move order to dispatch",
      });
    } finally {
      setIsApproving(false);
    }
  };

  const handleMarkAsReceived = async () => {
    if (!orderId || !onApprove) return;

    try {
      setIsMarkingAsReceived(true);
      await onApprove(orderId);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to mark order as received",
      });
    } finally {
      setIsMarkingAsReceived(false);
    }
  };
  return (
    <Container>
      <SectionHeader
        title={t("order.timeline")}
        icon={
          <Ionicons
            name="time-outline"
            size={20}
            color={theme.colors.primary}
          />
        }
      />

      <TimelineContainer>
        {orderStatusList.map((step, index) => {
          const { isCompleted, isActive } = getStepStatus(step.id);
          const isLast = index === orderStatusList.length - 1;
          return (
            <TimelineItem key={step.id.toString()}>
              <TimelineIndicator>
                {isCompleted ? (
                  <Ionicons
                    name="checkmark-circle"
                    size={24}
                    color={theme.colors.success || "#28A745"}
                  />
                ) : isActive ? (
                  step.id === OrderStatusEnum.Cancelled ? (
                    <Ionicons
                      name="close-circle"
                      size={24}
                      color={theme.colors.error || "#DC3545"}
                    />
                  ) : (
                    <Ionicons
                      name="radio-button-on"
                      size={24}
                      color={theme.colors.primary}
                    />
                  )
                ) : (
                  <Ionicons
                    name="radio-button-off"
                    size={24}
                    color={theme.colors.divider || "#E9ECEF"}
                  />
                )}
                <TimelineLine
                  isLast={isLast}
                  isCompleted={isCompleted}
                  isCancelled={
                    currentStatus === OrderStatusEnum.Cancelled &&
                    step.id >= OrderStatusEnum.Pending &&
                    step.id < OrderStatusEnum.Cancelled
                  }
                />
              </TimelineIndicator>

              <TimelineContent>
                <TimelineTitle isActive={isActive} isCompleted={isCompleted}>
                  {step.label}
                </TimelineTitle>
                {currentOrder?.is_paid === OrderPaid.Yes && (
                  <Fragment>
                    {shouldShowApprovalButtons(step.id) && (
                      <ApprovalButtonsContainer>
                        <ApprovalButton
                          variant="approve"
                          onPress={handleApprove}
                          disabled={isApproving || isRejecting}
                        >
                          <ApprovalButtonText>
                            {isApproving
                              ? t("order.approving")
                              : t("order.approve")}
                          </ApprovalButtonText>
                        </ApprovalButton>

                        <ApprovalButton
                          variant="reject"
                          onPress={handleReject}
                          disabled={isApproving || isRejecting}
                        >
                          <ApprovalButtonText>
                            {isRejecting
                              ? t("order.rejecting")
                              : t("order.reject")}
                          </ApprovalButtonText>
                        </ApprovalButton>
                      </ApprovalButtonsContainer>
                    )}

                     //Todo: temporary disabled now admin panel will be status changed

                    {/* {shouldShowMoveToInDispatchButton(step.id) && (
                      <ApprovalButtonsContainer>
                        <ApprovalButton
                          variant="approve"
                          onPress={handleMoveToInDispatch}
                          disabled={isApproving}
                        >
                          <ApprovalButtonText>
                            {isApproving
                              ? t("order.moving_to_in_dispatch")
                              : t("order.move_to_in_dispatch")}
                          </ApprovalButtonText>
                        </ApprovalButton>
                      </ApprovalButtonsContainer>
                    )}

                    {shouldShowDispatchButton(step.id) && (
                      <ApprovalButtonsContainer>
                        <ApprovalButton
                          variant="approve"
                          onPress={handleDispatch}
                          disabled={isDispatching}
                        >
                          <ApprovalButtonText>
                            {isDispatching
                              ? t("order.dispatching")
                              : t("order.dispatch")}
                          </ApprovalButtonText>
                        </ApprovalButton>
                      </ApprovalButtonsContainer>
                    )} */}

                    {shouldShowMarkAsReceivedButton(step.id) && (
                      <ApprovalButtonsContainer>
                        <ApprovalButton
                          variant="approve"
                          onPress={handleMarkAsReceived}
                          disabled={isMarkingAsReceived}
                        >
                          <ApprovalButtonText>
                            {isMarkingAsReceived
                              ? t("order.marking_as_received")
                              : t("order.mark_as_received")}
                          </ApprovalButtonText>
                        </ApprovalButton>
                      </ApprovalButtonsContainer>
                    )}
                  </Fragment>
                )}
              </TimelineContent>
            </TimelineItem>
          );
        })}
      </TimelineContainer>

      <DispatchModal
        visible={showDispatchModal}
        onCancel={handleDispatchCancel}
        onConfirm={handleDispatchConfirm}
        loading={isDispatching}
      />

      <RejectModal
        visible={showRejectModal}
        onCancel={handleRejectCancel}
        onConfirm={handleRejectConfirm}
        loading={isRejecting}
      />
    </Container>
  );
};

export default OrderTimeline;
