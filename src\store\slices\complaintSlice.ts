import { createSlice } from "@reduxjs/toolkit";
import { ComplaintState } from "../../types/complaint";
import {
  getComplaintsListAction,
  complaintInsertUpdateAction,
  getComplaintStatusAction,
  getComplaintIssueListAction,
  getServicePersonListAction,
  sendComplaintEstimationAction,
  getPartListAction,
  complaintStatusChangeAction,
} from "../actions/complaint";
import { logoutAction } from "../actions/auth";
import { logoutLocalAction } from "../actions/auth";

const initialState: ComplaintState = {
  complaints: [],
  complaintDetails: null,
  loading: false,
  error: null,
  complaintsLoading: false,
  isLoadingMore: false,
  filters: null,
  current_page: 1,
  last_page: 1,
  loadedPages: [],
  complaintStatus: [],
  complaintIssues: [],
  servicePersonList: [],
  servicePersonCurrentPage: 1,
  servicePersonLastPage: 1,
  servicePersonLoadedPages: [],
  estimationDetails: null,
  estimationLoading: false,
  partList: [], // <-- Add this line to fix linter error
};

const complaintSlice = createSlice({
  name: "complaint",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    resetFilters: (state) => {
      state.filters = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all complaints
      .addCase(getComplaintsListAction.pending, (state, action) => {
        const isPagination = action.meta.arg?.page && action.meta.arg.page > 1;
        if (isPagination) {
          state.isLoadingMore = true;
        } else {
          state.complaintsLoading = true;
        }
        state.error = null;
      })
      .addCase(getComplaintsListAction.fulfilled, (state, action) => {
        state.complaintsLoading = false;
        state.isLoadingMore = false;
        state.filters = action.payload.meta;
        const responseData = action.payload?.data;
        if (responseData) {
          state.current_page = responseData.current_page;
          state.last_page = responseData.last_page;

          if (!state.loadedPages.includes(responseData.current_page)) {
            state.loadedPages.push(responseData.current_page);
          }

          if (responseData.current_page === 1) {
            state.complaints = responseData.data;
            state.loadedPages = [1];
          } else {
            state.complaints = [...state.complaints, ...responseData.data];
          }
        }
      })
      .addCase(getComplaintsListAction.rejected, (state, action) => {
        state.complaintsLoading = false;
        state.isLoadingMore = false;
        state.error = action.error.message || "Failed to load complaints";
      })
      // Create complaint
      .addCase(complaintInsertUpdateAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(complaintInsertUpdateAction.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload && action.payload.data) {
          state.complaints.unshift(action.payload.data);
        }
      })
      .addCase(complaintInsertUpdateAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create complaint";
      })
      // Get complaint status
      .addCase(getComplaintStatusAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getComplaintStatusAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaintStatus = action.payload.data;
      })
      // Get complaint issue list
      .addCase(getComplaintIssueListAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getComplaintIssueListAction.fulfilled, (state, action) => {
        state.loading = false;
        state.complaintIssues = action.payload.data;
      })
      // Get service person list
      .addCase(getServicePersonListAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getServicePersonListAction.fulfilled, (state, action) => {
        state.loading = false;
        const responseData = action.payload;
        if (responseData) {
          // Handle pagination for service person list
          state.servicePersonCurrentPage =
            responseData.pagination?.current_page || 1;
          state.servicePersonLastPage = responseData.pagination?.last_page || 1;

          if (!Array.isArray(state.servicePersonLoadedPages)) {
            state.servicePersonLoadedPages = [];
          }
          if (
            !state.servicePersonLoadedPages.includes(
              state.servicePersonCurrentPage
            )
          ) {
            state.servicePersonLoadedPages.push(state.servicePersonCurrentPage);
          }

          if (state.servicePersonCurrentPage === 1) {
            // Initial load or refresh - replace the list
            state.servicePersonList = responseData.data;
            state.servicePersonLoadedPages = [1];
          } else {
            // Load more - concatenate new data
            state.servicePersonList = [
              ...state.servicePersonList,
              ...responseData.data,
            ];
          }
        }
      })
      .addCase(getServicePersonListAction.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message || "Failed to load service person list";
      })
      // Remove duplicate .addCase for sendComplaintEstimationAction, getPartListAction, complaintStatusChangeAction
      .addCase(sendComplaintEstimationAction.pending, (state) => {
        state.estimationLoading = true;
        state.error = null;
      })
      .addCase(sendComplaintEstimationAction.fulfilled, (state, action) => {
        state.estimationLoading = false;
        state.estimationDetails = action.payload.data;
      })
      .addCase(sendComplaintEstimationAction.rejected, (state, action) => {
        state.estimationLoading = false;
        state.error = action.error.message || "Failed to fetch estimation";
      })
      .addCase(getPartListAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getPartListAction.fulfilled, (state, action) => {
        state.loading = false;
        state.partList = action.payload.data;
      })
      .addCase(getPartListAction.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch part list";
      })
      .addCase(complaintStatusChangeAction.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(complaintStatusChangeAction.fulfilled, (state, action) => {
        state.loading = false;
        const complaintId = action.meta.arg.complaint_id;
        const newStatus = action.meta.arg.status;
        const complaintIndex = state.complaints.findIndex(
          (complaint) => String(complaint.id) === String(complaintId)
        );
        if (complaintIndex !== -1) {
          state.complaints[complaintIndex].status = newStatus;
        }
      })
      .addCase(complaintStatusChangeAction.rejected, (state, action) => {
        state.loading = false;
        state.error =
          action.error.message || "Failed to change complaint status";
      })
      // Logout
      .addCase(logoutAction.fulfilled, () => {
        return initialState;
      })
      // Logout Local
      .addCase(logoutLocalAction.fulfilled, () => {
        return initialState;
      });
  },
});

export const { setFilters, resetFilters } = complaintSlice.actions;

export default complaintSlice.reducer;
