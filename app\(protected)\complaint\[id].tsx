import React, { useState } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/store";
import {
  complaintStatusChangeAction,
  getComplaintsListAction,
  sendComplaintEstimationAction,
  assignComplaintAction
} from "@/store/actions/complaint";
import { ScrollView, View } from "react-native";
import {
  Container,
  Content,
  Card,
  Title,
  Label,
  Value,
  StatusBadge,
  StatusText,
  ErrorText,
  EmptyContainer,
  EmptyText,
  ImageContainer,
  ComplaintImage,
  ActionButton,
  ActionButtonText,
  ActionButtonContainer,
  FullScreenModal,
  FullScreenImageContainer,
  FullScreenImage,
  CloseButton,
  SectionTitle,
  SectionCard,
  InfoRow,
  InfoLabel,
  InfoValue,
  StatusContainer,
  HistoryItem,
  HistoryText,
  HistoryDate,
} from "@/styles/Complaint.styles";
import { Header } from "@/components";
import { EstimationModal } from "@/components/molecules";
import { UserType } from "@/types/api";
import { ComplaintStatusEnum, ServicePerson } from "@/types/complaint";
import Toast from "react-native-toast-message";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import ServicePersonSelectionBottomSheet from "@/components/organisms/ServicePersonSelectionBottomSheet";
import { AddressDisplayCard } from "@/components/molecules";
import ImageGalleryModal from "@/components/organisms/ImageGalleryModal";
import RejectModal from "@/components/molecules/RejectModal";

const ComplaintDetailScreen = () => {
  const { id } = useLocalSearchParams();
  const { t } = useTranslation();
  const { complaints, complaintStatus, servicePersonList } = useAppSelector(
    (state) => state.complaints
  );
  const user = useAppSelector((state) => state.auth.user);
  console.log({ user });
  const isSalesPerson = UserType.SALESPERSON === user.role_id;
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showEstimationModal, setShowEstimationModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [rejectLoading, setRejectLoading] = useState(false);
  const dispatch = useAppDispatch();
  const complaintDetails = complaints.find(
    (complaint) => String(complaint.id) === String(id)
  ) as any;
  const [isServicePersonSheetOpen, setServicePersonSheetOpen] = useState(false);
  const [selectedServicePerson, setSelectedServicePerson] = useState(
    complaintDetails?.assign_to || null
  );
  const [galleryOpen, setGalleryOpen] = useState(false);
  const [galleryIndex, setGalleryIndex] = useState(0);

  const getNextStatusOptions = (
    currentStatus: ComplaintStatusEnum,
    userRole: UserType
  ) => {
    switch (Number(currentStatus)) {
      case ComplaintStatusEnum.Pending:
        if (
          userRole === UserType.SALESPERSON &&
          complaintDetails?.created_by_id !== user?.id
        ) {
          return [ComplaintStatusEnum.InReview];
        }
        return [];
      case ComplaintStatusEnum.InReview:
        if (userRole === UserType.SERVICEPERSON) {
          return [
            ComplaintStatusEnum.EstimationSent,
            ComplaintStatusEnum.Rejected,
          ];
        }
        if (userRole === UserType.SALESPERSON) {
          return selectedServicePerson ? [] : [ComplaintStatusEnum.Assigned];
        }
        return [];
      case ComplaintStatusEnum.EstimationSent:
        if (
          userRole === UserType.CUSTOMER ||
          userRole === UserType.SALESPERSON ||
          userRole === UserType.VENDOR
        ) {
          return [ComplaintStatusEnum.Approved, ComplaintStatusEnum.Rejected];
        }
        return [];
      case ComplaintStatusEnum.Approved:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.RepairInProgress];
        }
        return [];
      case ComplaintStatusEnum.RepairInProgress:
        if (userRole === UserType.SERVICEPERSON) {
          return [ComplaintStatusEnum.Resolved];
        }
        return [];
      default:
        return [];
    }
  };

  const handleComplaintStatusChange = async (status: ComplaintStatusEnum) => {
    if (status === ComplaintStatusEnum.Assigned) {
      setServicePersonSheetOpen(true);
      return;
    }
    if (!complaintDetails) return;

    if (
      status === ComplaintStatusEnum.EstimationSent &&
      user.role_id === UserType.SERVICEPERSON
    ) {
      setShowEstimationModal(true);
      return;
    }

    if (status === ComplaintStatusEnum.Rejected) {
      setShowRejectModal(true);
      return;
    }
    try {
      setIsLoading(true);
      await dispatch(
        complaintStatusChangeAction({
          complaint_id: complaintDetails.id,
          status,
        })
      ).unwrap();

      await dispatch(getComplaintsListAction({ page: 1 })).unwrap();

      Toast.show({
        type: "success",
        text1: t("complaint.status_change_success"),
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssignServicePerson = async (servicePerson: ServicePerson) => {
    if (!servicePerson) return;
    setSelectedServicePerson(servicePerson);
    setServicePersonSheetOpen(false);
    try {
      await dispatch(
        assignComplaintAction({
          slug: complaintDetails.slug,
          assign_to_id: servicePerson.user_id,
        })
      ).unwrap();
    } catch (err: any) {
      Toast.show({ type: "error", text1: err.message });
    } finally {
    }
  };

  const handleEstimationSubmit = async (data: any) => {
    if (!complaintDetails) return;

    try {
      setIsLoading(true);

      // Send the estimation action with complaint_id
      await dispatch(
        sendComplaintEstimationAction({
          slug: data.slug,
          estimate_time: data.estimate_time,
          product: data.product,
          quantity: data.quantity,
        })
      ).unwrap();

      // Update complaint status to approved
      await dispatch(
        complaintStatusChangeAction({
          complaint_id: complaintDetails.id,
          status: ComplaintStatusEnum.Approved,
        })
      ).unwrap();

      await dispatch(getComplaintsListAction({ page: 1 })).unwrap();

      setShowEstimationModal(false);

      Toast.show({
        type: "success",
        text1: t("complaint.estimation_sent_success") || "Estimation sent successfully",
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEstimationCancel = () => {
    setShowEstimationModal(false);
  };

  const handleRejectConfirm = async (data: { reason: string }) => {
    if (!complaintDetails) return;
    try {
      setRejectLoading(true);
      await dispatch(
        complaintStatusChangeAction({
          complaint_id: complaintDetails.id,
          status: ComplaintStatusEnum.Rejected,
          description: data.reason,
        })
      ).unwrap();
      await dispatch(getComplaintsListAction({ page: 1 })).unwrap();
      setShowRejectModal(false);
      Toast.show({
        type: "success",
        text1: t("complaint.status_change_success"),
      });
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message,
      });
    } finally {
      setRejectLoading(false);
    }
  };

  const getStatusLabel = (status: number) => {
    return complaintStatus.find(
      (statusItem) => statusItem.id === Number(status)
    )?.label;
  };
  if (!complaintDetails) {
    return (
      <>
        <Header
          title={t("complaint.title")}
          showBack
          onBackPress={() => router.back()}
          showCart={false}
        />
        <Container>
          <EmptyContainer>
            <EmptyText>{t("complaint.not_found")}</EmptyText>
          </EmptyContainer>
        </Container>
      </>
    );
  }
  return (
    <>
      <Header
        title={t("complaint.title")}
        showBack
        onBackPress={() => router.back()}
        showCart={false}
      />
      <Container>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Content>
            <SectionCard>
              <Title>{complaintDetails?.complaint_number}</Title>

              <StatusContainer>
                <Label>{t("complaint.status")}</Label>
                <StatusBadge status={complaintDetails?.status || 1}>
                  <StatusText status={complaintDetails?.status || 1}>
                    {getStatusLabel(complaintDetails?.status || 1)}
                  </StatusText>
                </StatusBadge>
              </StatusContainer>

              <InfoRow>
                <InfoLabel>{t("complaint.type")}</InfoLabel>
                <InfoValue>
                  {complaintDetails?.complaint_type === "1"
                    ? t("complaint.brands.ozone")
                    : t("complaint.brands.third_party")}
                </InfoValue>
              </InfoRow>

              <InfoRow>
                <InfoLabel>{t("complaint.created_by")}</InfoLabel>
                <InfoValue>
                  {complaintDetails?.created_by?.name ||
                    t("common.not_available")}
                </InfoValue>
              </InfoRow>

              <InfoRow>
                <InfoLabel>{t("complaint.created_at")}</InfoLabel>
                <InfoValue>
                  {complaintDetails?.created_at
                    ? new Date(complaintDetails.created_at).toLocaleDateString()
                    : t("common.not_available")}
                </InfoValue>
              </InfoRow>
            </SectionCard>

            {/* Complaint Details Section */}
            <SectionCard>
              <SectionTitle>{t("complaint.details")}</SectionTitle>

              {complaintDetails?.description && (
                <InfoRow>
                  <InfoLabel>{t("complaint.description")}</InfoLabel>
                  <InfoValue>{complaintDetails.description}</InfoValue>
                </InfoRow>
              )}

              {complaintDetails?.complaint_issue && (
                <InfoRow>
                  <InfoLabel>{t("complaint.issue")}</InfoLabel>
                  <InfoValue>{complaintDetails.complaint_issue.name}</InfoValue>
                </InfoRow>
              )}
            </SectionCard>

            {/* Product Information Section */}
            {(complaintDetails?.product_qr_code ||
              complaintDetails?.brand_name) && (
              <SectionCard>
                <SectionTitle>{t("complaint.product_info")}</SectionTitle>

                {complaintDetails?.product_qr_code && (
                  <InfoRow>
                    <InfoLabel>{t("complaint.serial_number")}</InfoLabel>
                    <InfoValue>
                      {complaintDetails.product_qr_code.serial_no}
                    </InfoValue>
                  </InfoRow>
                )}

                {complaintDetails?.brand_name && (
                  <InfoRow>
                    <InfoLabel>{t("complaint.brand_name")}</InfoLabel>
                    <InfoValue>{complaintDetails.brand_name}</InfoValue>
                  </InfoRow>
                )}
              </SectionCard>
            )}

            {/* Product Photos Section */}
            {complaintDetails?.product_serial_photo &&
              complaintDetails.product_serial_photo.length > 0 && (
                <SectionCard>
                  <SectionTitle>{t("complaint.product_photos")}</SectionTitle>
                  <ImageContainer>
                    {complaintDetails.product_serial_photo.map(
                      (photo: any, index: number) => (
                        <ComplaintImage
                          key={photo.id}
                          source={{ uri: photo.url }}
                          contentFit="contain"
                          onPress={() => {
                            setGalleryIndex(index);
                            setGalleryOpen(true);
                          }}
                        />
                      )
                    )}
                  </ImageContainer>
                </SectionCard>
              )}

            {/* Address Information Section */}
            {(complaintDetails?.billing_address ||
              complaintDetails?.shipping_address ||
              complaintDetails?.billing_address_id ||
              complaintDetails?.shipping_address_id) && (
              <SectionCard>
                <SectionTitle>{t("complaint.addresses")}</SectionTitle>

                {/* Show full address if available, else fallback to ID */}
                {complaintDetails?.billing_address ? (
                  <AddressDisplayCard
                    address={complaintDetails.billing_address}
                    type="billing"
                  />
                ) : complaintDetails?.billing_address_id ? (
                  <InfoRow>
                    <InfoLabel>{t("complaint.billing_address_id")}</InfoLabel>
                    <InfoValue>{complaintDetails.billing_address_id}</InfoValue>
                  </InfoRow>
                ) : null}

                {complaintDetails?.shipping_address ? (
                  <AddressDisplayCard
                    address={complaintDetails.shipping_address}
                    type="shipping"
                  />
                ) : complaintDetails?.shipping_address_id ? (
                  <InfoRow>
                    <InfoLabel>{t("complaint.shipping_address_id")}</InfoLabel>
                    <InfoValue>
                      {complaintDetails.shipping_address_id}
                    </InfoValue>
                  </InfoRow>
                ) : null}
              </SectionCard>
            )}

            {selectedServicePerson &&
              Number(complaintDetails.status) ===
                ComplaintStatusEnum.InReview && (
                <SectionCard>
                  <SectionTitle>
                    {t("complaint.selected_service_person")}
                  </SectionTitle>
                  <InfoRow>
                    <InfoLabel>{t("common.name")}</InfoLabel>
                    <InfoValue>{selectedServicePerson.name}</InfoValue>
                  </InfoRow>
                  <InfoRow>
                    <InfoLabel>{t("common.email")}</InfoLabel>
                    <InfoValue>{selectedServicePerson.email}</InfoValue>
                  </InfoRow>
                  <View style={{ flexDirection: "row", gap: 8, marginTop: 12 }}>
                    <ActionButton
                      style={{
                        backgroundColor: theme.colors.gray,
                        marginRight: 8,
                      }}
                      onPress={() => setSelectedServicePerson(null)}
                    >
                      <ActionButtonText>{t("common.remove")}</ActionButtonText>
                    </ActionButton>
                    <ActionButton
                      style={{ backgroundColor: theme.colors.primary }}
                      onPress={() => setServicePersonSheetOpen(true)}
                    >
                      <ActionButtonText>{t("common.change")}</ActionButtonText>
                    </ActionButton>
                  </View>
                </SectionCard>
              )}

            {complaintDetails?.histories &&
              complaintDetails.histories.length > 0 && (
                <SectionCard>
                  <SectionTitle>{t("complaint.history")}</SectionTitle>
                  {complaintDetails.histories.map(
                    (history: any, index: number) => (
                      <HistoryItem key={index}>
                        <HistoryText>
                          {t("complaint.status_changed_to")}{" "}
                          {getStatusLabel(history.new_status)}
                        </HistoryText>
                        <HistoryDate>
                          {new Date(history.created_at).toLocaleDateString()}
                        </HistoryDate>
                      </HistoryItem>
                    )
                  )}
                </SectionCard>
              )}
            {user?.role_id &&
              getNextStatusOptions(complaintDetails.status, user.role_id)
                .length > 0 && (
                <>
                  <Label>{t("complaint.update_status")}</Label>
                  <ActionButtonContainer>
                    {getNextStatusOptions(
                      complaintDetails.status,
                      user.role_id
                    ).map((status) => (
                      <ActionButton
                        key={status}
                        onPress={() => handleComplaintStatusChange(status)}
                      >
                        <ActionButtonText>
                          {t(`status.${status}`)}
                        </ActionButtonText>
                      </ActionButton>
                    ))}
                  </ActionButtonContainer>
                </>
              )}
          </Content>
        </ScrollView>
      </Container>

      <ImageGalleryModal
        visible={galleryOpen}
        images={
          complaintDetails?.product_serial_photo?.map((p: any) => p.url) || []
        }
        initialIndex={galleryIndex}
        onRequestClose={() => setGalleryOpen(false)}
      />

      <ServicePersonSelectionBottomSheet
        isVisible={isServicePersonSheetOpen}
        onClose={() => setServicePersonSheetOpen(false)}
        onSelect={handleAssignServicePerson}
      />

      <EstimationModal
        visible={showEstimationModal}
        onClose={() => setShowEstimationModal(false)}
        onConfirm={handleEstimationSubmit}
        loading={isLoading}
      />

      <RejectModal
        visible={showRejectModal}
        onCancel={() => setShowRejectModal(false)}
        onConfirm={handleRejectConfirm}
        loading={rejectLoading}
      />
    </>
  );
};

export default ComplaintDetailScreen;
